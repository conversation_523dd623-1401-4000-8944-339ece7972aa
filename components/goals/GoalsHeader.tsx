import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Plus,
  Filter,
  BarChart3,
  Search,
} from 'lucide-react-native';
import Isotope<PERSON>ogo from '@/components/IsotopeLogo';
import { Task } from '@/types/app';

interface GoalsHeaderProps {
  showStats: boolean;
  setShowStats: (show: boolean) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedView: 'all' | 'todo' | 'in_progress' | 'completed';
  setSelectedView: (view: 'all' | 'todo' | 'in_progress' | 'completed') => void;
  onAddTask: () => void;
  onOpenFilter: () => void;
  tasks: Task[];
  screenWidth: number;
}

const GoalsHeader: React.FC<GoalsHeaderProps> = ({
  showStats,
  setShowStats,
  searchQuery,
  setSearchQuery,
  selectedView,
  setSelectedView,
  onAddTask,
  onOpenFilter,
  tasks,
  screenWidth, // Destructure screenWidth
}) => {
  const todoTasks = tasks.filter(task => task.status === 'todo');
  const inProgressTasks = tasks.filter(task => task.status === 'in_progress');
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const overdueTasks = tasks.filter(task => 
    task.due_date && 
    task.due_date < new Date() && 
    task.due_date.toDateString() !== new Date().toDateString()
  );

  const localScale = (size: number) => scale(size, screenWidth);

  return (
    <LinearGradient
      colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
      style={styles(screenWidth).headerGradient}
    >
      <View style={styles(screenWidth).header}>
        <View style={styles(screenWidth).headerLeft}>
          <IsotopeLogo size="medium" />
          <Text style={styles(screenWidth).subtitle}>Task Management</Text>
        </View>
        <View style={styles(screenWidth).headerRight}>
          <TouchableOpacity style={styles(screenWidth).statsButton} onPress={() => setShowStats(!showStats)}>
            <BarChart3 size={localScale(20)} color="#6366F1" />
          </TouchableOpacity>
          <TouchableOpacity style={styles(screenWidth).filterButton} onPress={onOpenFilter}>
            <Filter size={localScale(20)} color="#6366F1" />
          </TouchableOpacity>
          <TouchableOpacity style={styles(screenWidth).addButton} onPress={onAddTask}>
            <Plus size={localScale(20)} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles(screenWidth).searchContainer}>
        <View style={styles(screenWidth).searchBar}>
          <Search size={localScale(20)} color="#6B7280" />
          <TextInput
            style={styles(screenWidth).searchInput}
            placeholder="Search tasks..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {/* Quick Stats */}
      {showStats && (
        <Animated.View style={styles(screenWidth).statsContainer}>
          <View style={styles(screenWidth).statCard}>
            <Text style={styles(screenWidth).statNumber}>{todoTasks.length}</Text>
            <Text style={styles(screenWidth).statLabel}>To Do</Text>
          </View>
          <View style={styles(screenWidth).statCard}>
            <Text style={styles(screenWidth).statNumber}>{inProgressTasks.length}</Text>
            <Text style={styles(screenWidth).statLabel}>In Progress</Text>
          </View>
          <View style={styles(screenWidth).statCard}>
            <Text style={styles(screenWidth).statNumber}>{completedTasks.length}</Text>
            <Text style={styles(screenWidth).statLabel}>Completed</Text>
          </View>
          <View style={styles(screenWidth).statCard}>
            <Text style={[styles(screenWidth).statNumber, { color: '#EF4444' }]}>{overdueTasks.length}</Text>
            <Text style={styles(screenWidth).statLabel}>Overdue</Text>
          </View>
        </Animated.View>
      )}

      {/* Filter Tabs */}
      <View style={styles(screenWidth).filterTabs}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles(screenWidth).tabsContainer}>
          {['all', 'todo', 'in_progress', 'completed'].map((view) => (
            <TouchableOpacity
              key={view}
              style={[
                styles(screenWidth).filterTab,
                selectedView === view && styles(screenWidth).activeFilterTab
              ]}
              onPress={() => setSelectedView(view as any)}
            >
              <Text style={[
                styles(screenWidth).filterTabText,
                selectedView === view && styles(screenWidth).activeFilterTabText
              ]}>
                {view === 'all' ? 'All' :
                 view === 'todo' ? 'To Do' :
                 view === 'in_progress' ? 'In Progress' : 'Completed'}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </LinearGradient>
  );
};

const scale = (size: number, screenWidth: number) => (screenWidth / 375) * size;

const styles = (screenWidth: number) => StyleSheet.create({
  headerGradient: {
    paddingTop: scale(60, screenWidth),
    paddingBottom: scale(20, screenWidth),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: scale(24, screenWidth),
  },
  subtitle: {
    fontSize: scale(14, screenWidth),
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: scale(4, screenWidth),
  },
  headerLeft: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: scale(12, screenWidth),
  },
  statsButton: {
    backgroundColor: '#FFFFFF',
    width: scale(40, screenWidth),
    height: scale(40, screenWidth),
    borderRadius: scale(20, screenWidth),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2, screenWidth) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4, screenWidth),
    elevation: scale(3, screenWidth),
  },
  filterButton: {
    backgroundColor: '#FFFFFF',
    width: scale(40, screenWidth),
    height: scale(40, screenWidth),
    borderRadius: scale(20, screenWidth),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2, screenWidth) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4, screenWidth),
    elevation: scale(3, screenWidth),
  },
  addButton: {
    width: scale(44, screenWidth),
    height: scale(44, screenWidth),
    backgroundColor: '#6366F1',
    borderRadius: scale(22, screenWidth),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: scale(4, screenWidth) },
    shadowOpacity: 0.3,
    shadowRadius: scale(8, screenWidth),
    elevation: scale(6, screenWidth),
  },
  searchContainer: {
    paddingHorizontal: scale(20, screenWidth),
    paddingTop: scale(16, screenWidth),
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: scale(12, screenWidth),
    paddingHorizontal: scale(16, screenWidth),
    paddingVertical: scale(12, screenWidth),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2, screenWidth) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4, screenWidth),
    elevation: scale(3, screenWidth),
  },
  searchInput: {
    flex: 1,
    marginLeft: scale(12, screenWidth),
    fontSize: scale(16, screenWidth),
    color: '#1F2937',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: scale(20, screenWidth),
    paddingTop: scale(16, screenWidth),
    gap: scale(12, screenWidth),
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: scale(12, screenWidth),
    padding: scale(16, screenWidth),
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: scale(2, screenWidth) },
    shadowOpacity: 0.1,
    shadowRadius: scale(4, screenWidth),
    elevation: scale(3, screenWidth),
  },
  statNumber: {
    fontSize: scale(24, screenWidth),
    fontWeight: 'bold',
    color: '#1F2937',
  },
  statLabel: {
    fontSize: scale(12, screenWidth),
    color: '#6B7280',
    marginTop: scale(4, screenWidth),
  },
  filterTabs: {
    paddingTop: scale(16, screenWidth),
  },
  tabsContainer: {
    paddingHorizontal: scale(20, screenWidth),
  },
  filterTab: {
    paddingHorizontal: scale(20, screenWidth),
    paddingVertical: scale(8, screenWidth),
    marginRight: scale(12, screenWidth),
    borderRadius: scale(20, screenWidth),
    backgroundColor: '#FFFFFF',
    borderWidth: scale(1, screenWidth),
    borderColor: '#E5E7EB',
  },
  activeFilterTab: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  filterTabText: {
    fontSize: scale(14, screenWidth),
    color: '#6B7280',
    fontWeight: '500',
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
});

export default GoalsHeader;

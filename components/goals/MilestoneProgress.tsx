import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { Award } from 'lucide-react-native';
import { Task } from '@/types/app';

interface MilestoneProgressProps {
  tasks: Task[];
  screenWidth: number;
}

const MilestoneProgress: React.FC<MilestoneProgressProps> = ({ tasks, screenWidth }) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  const milestones = tasks.filter(task => task.is_milestone);
  const completedMilestones = milestones.filter(task => task.status === 'completed');
  const progressPercentage = milestones.length > 0 ? (completedMilestones.length / milestones.length) * 100 : 0;

  const animatedProgress = useSharedValue(0);

  useEffect(() => {
    animatedProgress.value = withTiming(progressPercentage, { duration: 1000 });
  }, [progressPercentage]);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${animatedProgress.value}%`,
  }));

  return (
    <View style={styles(screenWidth).milestoneProgress}>
      <View style={styles(screenWidth).milestoneHeader}>
        <Award size={localScale(20)} color="#F59E0B" />
        <Text style={styles(screenWidth).milestoneTitle}>Milestone Progress</Text>
      </View>
      <View style={styles(screenWidth).milestoneBar}>
        <Animated.View style={[styles(screenWidth).milestoneBarFill, animatedStyle]} />
      </View>
      <View style={styles(screenWidth).milestoneStats}>
        <Text style={styles(screenWidth).milestoneStatsText}>
          {completedMilestones.length} of {milestones.length} milestones completed
        </Text>
        <Text style={styles(screenWidth).milestonePercentage}>{Math.round(progressPercentage)}%</Text>
      </View>
    </View>
  );
};

const styles = (screenWidth: number) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    milestoneProgress: {
      backgroundColor: '#FFFFFF',
      borderRadius: localScale(16),
      padding: localScale(20),
      marginHorizontal: localScale(16),
      marginBottom: localScale(16),
      shadowColor: '#000',
      shadowOffset: { width: 0, height: localScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: localScale(4),
      elevation: localScale(3),
    },
    milestoneHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(16),
      gap: localScale(8),
    },
    milestoneTitle: {
      fontSize: localScale(18),
      fontWeight: '600',
      color: '#1F2937',
    },
    milestoneBar: {
      height: localScale(12),
      backgroundColor: '#FEF3C7',
      borderRadius: localScale(6),
      overflow: 'hidden',
      marginBottom: localScale(12),
    },
  milestoneBarFill: {
    height: '100%',
    backgroundColor: '#F59E0B',
    borderRadius: localScale(6),
  },
  milestoneStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  milestoneStatsText: {
    fontSize: localScale(14),
    color: '#6B7280',
  },
  milestonePercentage: {
    fontSize: localScale(16),
    fontWeight: '600',
    color: '#F59E0B',
  },
});
};

export default MilestoneProgress;

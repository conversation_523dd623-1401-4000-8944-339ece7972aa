import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import {
  Activity,
  Clock,
  TrendingUp,
} from 'lucide-react-native';
import { Task } from '@/types/app';

interface ProductivityAnalyticsProps {
  tasks: Task[];
  screenWidth: number;
}

const ProductivityAnalytics: React.FC<ProductivityAnalyticsProps> = ({ tasks, screenWidth }) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  // Calculate productivity metrics
  const completedTasks = tasks.filter(task => task.status === 'completed');
  const totalTasks = tasks.length;
  const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;

  // Calculate average completion time
  const tasksWithDuration = completedTasks.filter(task => task.actual_duration);
  const avgCompletionTime = tasksWithDuration.length > 0
    ? tasksWithDuration.reduce((sum, task) => sum + (task.actual_duration || 0), 0) / tasksWithDuration.length
    : 0;

  // Calculate productivity by priority
  const priorityStats = ['urgent', 'high', 'medium', 'low'].map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const priorityCompleted = priorityTasks.filter(task => task.status === 'completed');
    const rate = priorityTasks.length > 0 ? (priorityCompleted.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      total: priorityTasks.length,
      completed: priorityCompleted.length,
      rate,
      color: priority === 'urgent' ? '#EF4444' :
             priority === 'high' ? '#F59E0B' :
             priority === 'medium' ? '#3B82F6' : '#10B981'
    };
  });

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  return (
    <View style={styles(screenWidth).productivityAnalytics}>
      <Text style={styles(screenWidth).analyticsTitle}>Productivity Analytics</Text>

      {/* Overall Stats */}
      <View style={styles(screenWidth).analyticsOverview}>
        <View style={styles(screenWidth).analyticsCard}>
          <Activity size={localScale(20)} color="#3B82F6" />
          <Text style={styles(screenWidth).analyticsValue}>{Math.round(completionRate)}%</Text>
          <Text style={styles(screenWidth).analyticsLabel}>Completion Rate</Text>
        </View>
        <View style={styles(screenWidth).analyticsCard}>
          <Clock size={localScale(20)} color="#10B981" />
          <Text style={styles(screenWidth).analyticsValue}>{formatDuration(avgCompletionTime)}</Text>
          <Text style={styles(screenWidth).analyticsLabel}>Avg. Time</Text>
        </View>
        <View style={styles(screenWidth).analyticsCard}>
          <TrendingUp size={localScale(20)} color="#F59E0B" />
          <Text style={styles(screenWidth).analyticsValue}>{completedTasks.length}</Text>
          <Text style={styles(screenWidth).analyticsLabel}>Completed</Text>
        </View>
      </View>

      {/* Priority Breakdown */}
      <View style={styles(screenWidth).priorityBreakdown}>
        <Text style={styles(screenWidth).breakdownTitle}>Completion by Priority</Text>
        {priorityStats.map((stat) => (
          <View key={stat.priority} style={styles(screenWidth).priorityRow}>
            <View style={styles(screenWidth).priorityInfo}>
              <View style={[styles(screenWidth).priorityDot, { backgroundColor: stat.color }]} />
              <Text style={styles(screenWidth).priorityName}>
                {stat.priority.charAt(0).toUpperCase() + stat.priority.slice(1)}
              </Text>
            </View>
            <View style={styles(screenWidth).priorityStats}>
              <Text style={styles(screenWidth).priorityCount}>{stat.completed}/{stat.total}</Text>
              <Text style={[styles(screenWidth).priorityRate, { color: stat.color }]}>
                {Math.round(stat.rate)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = (screenWidth: number) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    productivityAnalytics: {
      backgroundColor: '#FFFFFF',
      borderRadius: localScale(16),
      padding: localScale(20),
      marginHorizontal: localScale(16),
      marginBottom: localScale(16),
      shadowColor: '#000',
      shadowOffset: { width: 0, height: localScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: localScale(4),
      elevation: localScale(3),
    },
  analyticsTitle: {
    fontSize: localScale(18),
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: localScale(16),
  },
  analyticsOverview: {
    flexDirection: 'row',
    gap: localScale(12),
    marginBottom: localScale(20),
  },
  analyticsCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: localScale(12),
    padding: localScale(16),
    alignItems: 'center',
    gap: localScale(8),
  },
  analyticsValue: {
    fontSize: localScale(20),
    fontWeight: '700',
    color: '#1F2937',
  },
  analyticsLabel: {
    fontSize: localScale(12),
    color: '#6B7280',
    textAlign: 'center',
  },
  priorityBreakdown: {
    gap: localScale(12),
  },
  breakdownTitle: {
    fontSize: localScale(16),
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: localScale(8),
  },
  priorityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: localScale(8),
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: localScale(8),
  },
  priorityDot: {
    width: localScale(8),
    height: localScale(8),
    borderRadius: localScale(4),
  },
  priorityName: {
    fontSize: localScale(14),
    color: '#374151',
    fontWeight: '500',
  },
  priorityStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: localScale(12),
  },
  priorityCount: {
    fontSize: localScale(14),
    color: '#6B7280',
  },
  priorityRate: {
    fontSize: localScale(14),
    fontWeight: '600',
  },
});
};

export default ProductivityAnalytics;

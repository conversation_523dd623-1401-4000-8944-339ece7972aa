import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated from 'react-native-reanimated';
import { Task } from '@/types/app';

interface ProgressChartProps {
  tasks: Task[];
  screenWidth: number;
}

const ProgressChart: React.FC<ProgressChartProps> = ({ tasks, screenWidth }) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  const chartWidth = screenWidth - localScale(32);
  const chartHeight = localScale(200);

  // Calculate priority-based progress
  const priorities = ['urgent', 'high', 'medium', 'low'] as const;
  const priorityColors = {
    urgent: '#DC2626',
    high: '#EA580C',
    medium: '#D97706',
    low: '#65A30D'
  };

  const priorityProgress = priorities.map(priority => {
    const priorityTasks = tasks.filter(task => task.priority === priority);
    const completedTasks = priorityTasks.filter(task => task.status === 'completed');
    const progress = priorityTasks.length > 0 ? (completedTasks.length / priorityTasks.length) * 100 : 0;

    return {
      priority,
      color: priorityColors[priority],
      progress,
      total: priorityTasks.length,
      completed: completedTasks.length,
    };
  });

  const maxProgress = Math.max(...priorityProgress.map(pp => pp.progress), 1);

  return (
    <View style={styles(screenWidth).progressChart}>
      <Text style={styles(screenWidth).chartTitle}>Progress by Priority</Text>
      <View style={styles(screenWidth).chartContainer}>
        {priorityProgress.map((item) => (
          <View key={item.priority} style={styles(screenWidth).chartBar}>
            <View style={styles(screenWidth).barContainer}>
              <View style={[styles(screenWidth).barBackground, { width: chartWidth * 0.7 }]}>
                <Animated.View
                  style={[
                    styles(screenWidth).barFill,
                    {
                      backgroundColor: item.color,
                      width: `${item.progress}%`,
                    }
                  ]}
                />
              </View>
              <Text style={styles(screenWidth).progressPercentage}>{Math.round(item.progress)}%</Text>
            </View>
            <View style={styles(screenWidth).barInfo}>
              <Text style={styles(screenWidth).categoryName}>{item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}</Text>
              <Text style={styles(screenWidth).taskCount}>{item.completed}/{item.total} tasks</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = (screenWidth: number) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    progressChart: {
      backgroundColor: '#FFFFFF',
      borderRadius: localScale(16),
      padding: localScale(20),
      margin: localScale(16),
      shadowColor: '#000',
      shadowOffset: { width: 0, height: localScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: localScale(4),
      elevation: localScale(3),
    },
    chartTitle: {
      fontSize: localScale(18),
      fontWeight: '600',
      color: '#1F2937',
      marginBottom: localScale(16),
    },
    chartContainer: {
      gap: localScale(12),
    },
    chartBar: {
      gap: localScale(8),
    },
    barContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(12),
    },
    barBackground: {
      height: localScale(8),
      backgroundColor: '#F3F4F6',
      borderRadius: localScale(4),
      overflow: 'hidden',
    },
  barFill: {
    height: '100%',
    borderRadius: localScale(4),
  },
    progressPercentage: {
      fontSize: localScale(12),
      fontWeight: '600',
      color: '#6B7280',
      minWidth: localScale(35),
      textAlign: 'right',
    },
    barInfo: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    categoryName: {
      fontSize: localScale(14),
      fontWeight: '500',
      color: '#374151',
    },
    taskCount: {
      fontSize: localScale(12),
      color: '#6B7280',
    },
  });
};

export default ProgressChart;

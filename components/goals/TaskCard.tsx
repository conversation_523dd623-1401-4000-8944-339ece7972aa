import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import Animated from 'react-native-reanimated';
import {
  Star,
  CheckCircle,
  Award,
  Calendar as CalendarIcon,
  MoreVertical,
} from 'lucide-react-native';
import { Task } from '@/types/app';

interface TaskCardProps {
  task: Task;
  onToggleComplete: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUpdateProgress: (progress: number) => void;
  getPriorityColor: (priority: Task['priority']) => string;
  getPriorityIcon: (priority: Task['priority']) => React.ReactNode;
  getStatusIcon: (status: Task['status']) => React.ReactNode;
  formatDate: (date: Date) => string;
  isOverdue: (date: Date) => boolean;
  getSubjectById: (id: string) => any;
  screenWidth: number;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onToggleComplete,
  onEdit,
  onUpdateProgress,
  getPriorityColor,
  getPriorityIcon,
  getStatusIcon,
  formatDate,
  isOverdue,
  getSubjectById,
  screenWidth,
}) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  const subject = task.subject_id ? getSubjectById(task.subject_id) : null;
  const isTaskOverdue = task.due_date && isOverdue(task.due_date);

  return (
    <View style={[
      styles(screenWidth).taskCard,
      isTaskOverdue && styles(screenWidth).overdueCard,
      task.status === 'completed' && styles(screenWidth).completedCard
    ]}>
      {/* Task Header */}
      <View style={styles(screenWidth).taskHeader}>
        <TouchableOpacity onPress={onToggleComplete} style={styles(screenWidth).statusButton}>
          {getStatusIcon(task.status)}
        </TouchableOpacity>

        <View style={styles(screenWidth).taskInfo}>
          <View style={styles(screenWidth).taskTitleRow}>
            <Text style={[
              styles(screenWidth).taskTitle,
              task.status === 'completed' && styles(screenWidth).completedTitle
            ]}>
              {task.title}
            </Text>
            {task.is_milestone && (
              <View style={styles(screenWidth).milestoneTag}>
                <Star size={localScale(12)} color="#F59E0B" />
              </View>
            )}
          </View>

          {task.description && (
            <Text style={[
              styles(screenWidth).taskDescription,
              task.status === 'completed' && styles(screenWidth).completedDescription
            ]}>
              {task.description}
            </Text>
          )}

          {/* Tags */}
          {task.tags.length > 0 && (
            <View style={styles(screenWidth).tagsContainer}>
              {task.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles(screenWidth).tag}>
                  <Text style={styles(screenWidth).tagText}>{tag}</Text>
                </View>
              ))}
              {task.tags.length > 3 && (
                <Text style={styles(screenWidth).moreTagsText}>+{task.tags.length - 3}</Text>
              )}
            </View>
          )}

          {/* Subject */}
          {subject && (
            <View style={styles(screenWidth).subjectTag}>
              <View style={[styles(screenWidth).subjectDot, { backgroundColor: subject.color }]} />
              <Text style={styles(screenWidth).subjectName}>{subject.name}</Text>
            </View>
          )}
        </View>

        <TouchableOpacity onPress={onEdit} style={styles(screenWidth).editButton}>
          <MoreVertical size={localScale(16)} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Enhanced Progress Bar */}
      {task.progress_percentage > 0 && (
        <View style={styles(screenWidth).progressContainer}>
          <View style={styles(screenWidth).progressHeader}>
            <Text style={styles(screenWidth).progressLabel}>Progress</Text>
            <View style={styles(screenWidth).progressBadge}>
              <Text style={styles(screenWidth).progressText}>{task.progress_percentage}%</Text>
            </View>
          </View>
          <View style={styles(screenWidth).progressBar}>
            <Animated.View
              style={[
                styles(screenWidth).progressFill,
                {
                  width: `${task.progress_percentage}%`,
                  backgroundColor: getPriorityColor(task.priority)
                }
              ]}
            />
            {task.progress_percentage >= 100 && (
              <View style={styles(screenWidth).completionIndicator}>
                <CheckCircle size={localScale(12)} color="#10B981" />
              </View>
            )}
          </View>
          {task.is_milestone && (
            <View style={styles(screenWidth).milestoneIndicator}>
              <Award size={localScale(14)} color="#F59E0B" />
              <Text style={styles(screenWidth).milestoneText}>Milestone</Text>
            </View>
          )}

          {/* Interactive Progress Slider */}
          {task.status !== 'completed' && task.status !== 'cancelled' && (
            <View style={styles(screenWidth).progressSliderContainer}>
              <Text style={styles(screenWidth).sliderLabel}>Update Progress</Text>
              <View style={styles(screenWidth).progressSlider}>
                <TouchableOpacity
                  style={[styles(screenWidth).progressSliderTrack, { width: '100%' }]}
                  onPress={(event) => {
                    const { locationX } = event.nativeEvent;
                    const sliderWidth = screenWidth - (2 * localScale(16)); // Approximate slider width
                    const newProgress = Math.round((locationX / sliderWidth) * 100);
                    const clampedProgress = Math.max(0, Math.min(100, newProgress));
                    onUpdateProgress(clampedProgress);
                  }}
                >
                  <View style={[styles(screenWidth).progressSliderFill, { width: `${task.progress_percentage}%` }]} />
                  <View
                    style={[
                      styles(screenWidth).progressSliderThumb,
                      { left: `${task.progress_percentage}%` }
                    ]}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      )}

      {/* Task Meta */}
      <View style={styles(screenWidth).taskMeta}>
        <View style={styles(screenWidth).taskMetaLeft}>
          {/* Priority */}
          <View style={styles(screenWidth).priorityTag}>
            {getPriorityIcon(task.priority)}
            <Text style={[styles(screenWidth).priorityText, { color: getPriorityColor(task.priority) }]}>
              {task.priority}
            </Text>
          </View>
        </View>

        <View style={styles(screenWidth).taskMetaRight}>
          {/* Due Date */}
          {task.due_date && (
            <View style={styles(screenWidth).dueDateContainer}>
              <CalendarIcon size={localScale(14)} color={isTaskOverdue ? '#EF4444' : '#6B7280'} />
              <Text style={[
                styles(screenWidth).dueDateText,
                isTaskOverdue && styles(screenWidth).overdueDateText
              ]}>
                {formatDate(task.due_date)}
              </Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = (screenWidth: number) => {
  const localScale = (size: number) => (screenWidth / 375) * size;
  return StyleSheet.create({
    taskCard: {
      backgroundColor: '#FFFFFF',
      borderRadius: localScale(16),
      padding: localScale(16),
      marginBottom: localScale(12),
      shadowColor: '#000',
      shadowOffset: { width: 0, height: localScale(2) },
      shadowOpacity: 0.1,
      shadowRadius: localScale(4),
      elevation: localScale(3),
    },
    overdueCard: {
      borderLeftWidth: localScale(4),
      borderLeftColor: '#EF4444',
    },
    completedCard: {
      opacity: 0.7,
    },
    taskHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: localScale(12),
    },
    statusButton: {
      marginRight: localScale(12),
      marginTop: localScale(2),
    },
    taskInfo: {
      flex: 1,
    },
    taskTitleRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(4),
    },
    taskTitle: {
      fontSize: localScale(16),
      fontWeight: '600',
      color: '#1F2937',
      flex: 1,
    },
    completedTitle: {
      textDecorationLine: 'line-through',
      color: '#6B7280',
    },
    milestoneTag: {
      marginLeft: localScale(8),
    },
    taskDescription: {
      fontSize: localScale(14),
      color: '#6B7280',
      marginBottom: localScale(8),
      lineHeight: localScale(20),
    },
    completedDescription: {
      color: '#9CA3AF',
    },
    tagsContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(8),
    },
    tag: {
      backgroundColor: '#F3F4F6',
      paddingHorizontal: localScale(8),
      paddingVertical: localScale(4),
      borderRadius: localScale(12),
      marginRight: localScale(6),
    },
    tagText: {
      fontSize: localScale(12),
      color: '#6B7280',
      fontWeight: '500',
    },
    moreTagsText: {
      fontSize: localScale(12),
      color: '#9CA3AF',
      fontStyle: 'italic',
    },
    subjectTag: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: localScale(8),
    },
    subjectDot: {
      width: localScale(8),
      height: localScale(8),
      borderRadius: localScale(4),
      marginRight: localScale(6),
    },
    subjectName: {
      fontSize: localScale(12),
      color: '#6B7280',
      fontWeight: '500',
    },
    editButton: {
      padding: localScale(4),
    },
    progressContainer: {
      marginBottom: localScale(12),
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: localScale(8),
    },
    progressLabel: {
      fontSize: localScale(12),
      color: '#6B7280',
      fontWeight: '500',
    },
    progressBadge: {
      backgroundColor: '#F3F4F6',
      paddingHorizontal: localScale(8),
      paddingVertical: localScale(2),
      borderRadius: localScale(8),
    },
    progressText: {
      fontSize: localScale(12),
      color: '#374151',
      fontWeight: '600',
    },
    progressBar: {
      height: localScale(6),
      backgroundColor: '#F3F4F6',
      borderRadius: localScale(3),
      overflow: 'hidden',
      position: 'relative',
    },
    progressFill: {
      height: '100%',
      borderRadius: localScale(3),
    },
    completionIndicator: {
      position: 'absolute',
      right: localScale(4),
      top: localScale(-3),
    },
    milestoneIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: localScale(8),
      gap: localScale(4),
    },
    milestoneText: {
      fontSize: localScale(12),
      color: '#F59E0B',
      fontWeight: '500',
    },
    progressSliderContainer: {
      marginTop: localScale(12),
    },
    sliderLabel: {
      fontSize: localScale(12),
      color: '#6B7280',
      marginBottom: localScale(8),
    },
    progressSlider: {
      alignItems: 'center',
    },
    progressSliderTrack: {
      height: localScale(20),
      backgroundColor: '#F3F4F6',
      borderRadius: localScale(10),
      justifyContent: 'center',
      position: 'relative',
    },
    progressSliderFill: {
      height: localScale(6),
      backgroundColor: '#6366F1',
      borderRadius: localScale(3),
      position: 'absolute',
      left: 0,
    },
    progressSliderThumb: {
      width: localScale(16),
      height: localScale(16),
      backgroundColor: '#6366F1',
      borderRadius: localScale(8),
      position: 'absolute',
      marginLeft: localScale(-8),
    },
    taskMeta: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    taskMetaLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(4),
    },
    priorityTag: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(4),
    },
    priorityText: {
      fontSize: localScale(12),
      fontWeight: '500',
      textTransform: 'capitalize',
    },
    taskMetaRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    dueDateContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: localScale(4),
    },
    dueDateText: {
      fontSize: localScale(12),
      color: '#6B7280',
    },
    overdueDateText: {
      color: '#EF4444',
      fontWeight: '600',
    },
  });
};

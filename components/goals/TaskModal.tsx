import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import DateTimePicker from '@react-native-community/datetimepicker';
import {
  X,
  Calendar as CalendarIcon,
  Clock,
  CheckCircle,
  Star,
  Bell,
  Trash2,
} from 'lucide-react-native';
import SubjectPicker from '@/components/SubjectPicker';
import { Task } from '@/types/app';

interface TaskModalProps {
  visible: boolean;
  editingTask: Task | null;
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  dueDate: Date | undefined;
  setDueDate: (date: Date | undefined) => void;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  setPriority: (priority: 'low' | 'medium' | 'high' | 'urgent') => void;
  reminderEnabled: boolean;
  setReminderEnabled: (enabled: boolean) => void;
  reminderTime: Date | undefined;
  setReminderTime: (time: Date | undefined) => void;
  periodicReminders: boolean;
  setPeriodicReminders: (enabled: boolean) => void;
  reminderInterval: 'daily' | 'weekly' | 'monthly';
  setReminderInterval: (interval: 'daily' | 'weekly' | 'monthly') => void;
  selectedSubject: any;
  setSelectedSubject: (subject: any) => void;
  isMilestone: boolean;
  setIsMilestone: (milestone: boolean) => void;
  onClose: () => void;
  onSave: () => void;
  onDelete?: () => void;
  getPriorityColor: (priority: 'low' | 'medium' | 'high' | 'urgent') => string;
  getPriorityIcon: (priority: 'low' | 'medium' | 'high' | 'urgent') => React.ReactNode;
  modalScale: Animated.SharedValue<number>;
  screenWidth: number;
}

export const TaskModal: React.FC<TaskModalProps> = ({
  visible,
  editingTask,
  title,
  setTitle,
  description,
  setDescription,
  dueDate,
  setDueDate,
  priority,
  setPriority,
  reminderEnabled,
  setReminderEnabled,
  reminderTime,
  setReminderTime,
  periodicReminders,
  setPeriodicReminders,
  reminderInterval,
  setReminderInterval,
  selectedSubject,
  setSelectedSubject,
  isMilestone,
  setIsMilestone,
  onClose,
  onSave,
  onDelete,
  getPriorityColor,
  getPriorityIcon,
  modalScale,
  screenWidth,
}) => {
  // Date/Time picker states
  const [showDueDatePicker, setShowDueDatePicker] = useState(false);
  const [showReminderDatePicker, setShowReminderDatePicker] = useState(false);
  const [showReminderTimePicker, setShowReminderTimePicker] = useState(false);

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
  }));

  // Define localScale here to be used within the component's render logic
  const localScale = (size: number) => (screenWidth / 375) * size;

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles(screenWidth).modalOverlay}>
        <Animated.View style={[styles(screenWidth).modalContent, modalAnimatedStyle]}>
          <View style={styles(screenWidth).modalHeader}>
            <Text style={styles(screenWidth).modalTitle}>
              {editingTask ? 'Edit Task' : 'Create New Task'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={localScale(24)} color="#6B7280" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles(screenWidth).modalBody} showsVerticalScrollIndicator={false}>
            {/* Task Title */}
            <View style={styles(screenWidth).inputGroup}>
              <Text style={styles(screenWidth).inputLabel}>Task Title *</Text>
              <TextInput
                style={styles(screenWidth).textInput}
                value={title}
                onChangeText={setTitle}
                placeholder="Enter task title"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            {/* Description */}
            <View style={styles(screenWidth).inputGroup}>
              <Text style={styles(screenWidth).inputLabel}>Description</Text>
              <TextInput
                style={[styles(screenWidth).textInput, styles(screenWidth).textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Enter task description"
                placeholderTextColor="#9CA3AF"
                multiline
                numberOfLines={3}
              />
            </View>

            {/* Due Date */}
            <View style={styles(screenWidth).inputGroup}>
              <Text style={styles(screenWidth).inputLabel}>Due Date</Text>
              <TouchableOpacity
                style={styles(screenWidth).datePickerButton}
                onPress={() => setShowDueDatePicker(true)}
              >
                <CalendarIcon size={localScale(20)} color="#6B7280" />
                <Text style={styles(screenWidth).datePickerText}>
                  {dueDate ? dueDate.toLocaleDateString() : 'Select due date'}
                </Text>
              </TouchableOpacity>
              {showDueDatePicker && (
                <DateTimePicker
                  value={dueDate || new Date()}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={(event, selectedDate) => {
                    setShowDueDatePicker(false);
                    if (selectedDate) {
                      setDueDate(selectedDate);
                    }
                  }}
                />
              )}
            </View>

            {/* Priority */}
            <View style={styles(screenWidth).inputGroup}>
              <Text style={styles(screenWidth).inputLabel}>Priority</Text>
              <View style={styles(screenWidth).prioritySelector}>
                {(['low', 'medium', 'high', 'urgent'] as const).map((p) => (
                  <TouchableOpacity
                    key={p}
                    style={[
                      styles(screenWidth).priorityButton,
                      priority === p && styles(screenWidth).priorityButtonActive,
                      { borderColor: getPriorityColor(p) },
                    ]}
                    onPress={() => setPriority(p)}
                  >
                    {getPriorityIcon(p)}
                    <Text
                      style={[
                        styles(screenWidth).priorityButtonText,
                        priority === p && { color: getPriorityColor(p) },
                      ]}
                    >
                      {p.charAt(0).toUpperCase() + p.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Milestone Toggle */}
            <View style={styles(screenWidth).inputGroup}>
              <TouchableOpacity
                style={styles(screenWidth).checkboxRow}
                onPress={() => setIsMilestone(!isMilestone)}
              >
                <View style={[styles(screenWidth).checkbox, isMilestone && styles(screenWidth).checkboxActive]}>
                  {isMilestone && <CheckCircle size={localScale(16)} color="#FFFFFF" />}
                </View>
                <Text style={styles(screenWidth).checkboxLabel}>Mark as milestone</Text>
                <Star size={localScale(16)} color="#F59E0B" />
              </TouchableOpacity>
            </View>

            {/* Reminder Settings */}
            <View style={styles(screenWidth).inputGroup}>
              <TouchableOpacity
                style={styles(screenWidth).checkboxRow}
                onPress={() => setReminderEnabled(!reminderEnabled)}
              >
                <View style={[styles(screenWidth).checkbox, reminderEnabled && styles(screenWidth).checkboxActive]}>
                  {reminderEnabled && <CheckCircle size={localScale(16)} color="#FFFFFF" />}
                </View>
                <Text style={styles(screenWidth).checkboxLabel}>Enable reminders</Text>
                <Bell size={localScale(16)} color="#3B82F6" />
              </TouchableOpacity>

              {reminderEnabled && (
                <View style={styles(screenWidth).reminderTimeContainer}>
                  <Text style={styles(screenWidth).reminderTimeLabel}>Reminder Date & Time</Text>

                  {/* Reminder Date */}
                  <TouchableOpacity
                    style={styles(screenWidth).textInput}
                    onPress={() => setShowReminderDatePicker(true)}
                  >
                    <View style={styles(screenWidth).dateTimePickerRow}>
                      <CalendarIcon size={localScale(16)} color="#6B7280" />
                      <Text style={styles(screenWidth).dateTimePickerText}>
                        {reminderTime ? reminderTime.toLocaleDateString() : 'Select date'}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* Reminder Time */}
                  <TouchableOpacity
                    style={styles(screenWidth).textInput}
                    onPress={() => setShowReminderTimePicker(true)}
                  >
                    <View style={styles(screenWidth).dateTimePickerRow}>
                      <Clock size={localScale(16)} color="#6B7280" />
                      <Text style={styles(screenWidth).dateTimePickerText}>
                        {reminderTime ? reminderTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : 'Select time'}
                      </Text>
                    </View>
                  </TouchableOpacity>

                  {/* Periodic Reminders */}
                  <TouchableOpacity
                    style={styles(screenWidth).checkboxRow}
                    onPress={() => setPeriodicReminders(!periodicReminders)}
                  >
                    <View style={[styles(screenWidth).checkbox, periodicReminders && styles(screenWidth).checkboxActive]}>
                      {periodicReminders && <CheckCircle size={localScale(16)} color="#FFFFFF" />}
                    </View>
                    <Text style={styles(screenWidth).checkboxLabel}>Repeat reminders</Text>
                  </TouchableOpacity>

                  {periodicReminders && (
                    <View style={styles(screenWidth).reminderIntervalContainer}>
                      <Text style={styles(screenWidth).reminderIntervalLabel}>Repeat every:</Text>
                      <View style={styles(screenWidth).intervalSelector}>
                        {(['daily', 'weekly', 'monthly'] as const).map((interval) => (
                          <TouchableOpacity
                            key={interval}
                            style={[
                              styles(screenWidth).intervalButton,
                              reminderInterval === interval && styles(screenWidth).intervalButtonActive
                            ]}
                            onPress={() => setReminderInterval(interval)}
                          >
                            <Text style={[
                              styles(screenWidth).intervalButtonText,
                              reminderInterval === interval && styles(screenWidth).intervalButtonActiveText
                            ]}>
                              {interval.charAt(0).toUpperCase() + interval.slice(1)}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Date/Time Pickers */}
                  {showReminderDatePicker && (
                    <DateTimePicker
                      value={reminderTime || new Date()}
                      mode="date"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={(_, selectedDate) => {
                        setShowReminderDatePicker(false);
                        if (selectedDate) {
                          const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                          newDateTime.setFullYear(selectedDate.getFullYear());
                          newDateTime.setMonth(selectedDate.getMonth());
                          newDateTime.setDate(selectedDate.getDate());
                          setReminderTime(newDateTime);
                        }
                      }}
                    />
                  )}

                  {showReminderTimePicker && (
                    <DateTimePicker
                      value={reminderTime || new Date()}
                      mode="time"
                      display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                      onChange={(_, selectedTime) => {
                        setShowReminderTimePicker(false);
                        if (selectedTime) {
                          const newDateTime = reminderTime ? new Date(reminderTime) : new Date();
                          newDateTime.setHours(selectedTime.getHours());
                          newDateTime.setMinutes(selectedTime.getMinutes());
                          setReminderTime(newDateTime);
                        }
                      }}
                    />
                  )}

                  <Text style={styles(screenWidth).reminderHint}>
                    Set when you want to be reminded about this task
                  </Text>
                </View>
              )}
            </View>

            {/* Subject */}
            <View style={styles(screenWidth).inputGroup}>
              <SubjectPicker
                selectedSubject={selectedSubject}
                onSelectSubject={setSelectedSubject}
              />
            </View>
          </ScrollView>
          
          <View style={styles(screenWidth).modalActions}>
            {editingTask && onDelete && (
              <TouchableOpacity
                style={styles(screenWidth).deleteButton}
                onPress={() => {
                  onClose();
                  onDelete();
                }}
              >
                <Trash2 size={localScale(16)} color="#EF4444" />
                <Text style={styles(screenWidth).deleteText}>Delete</Text>
              </TouchableOpacity>
            )}

            <View style={styles(screenWidth).actionButtons}>
              <TouchableOpacity style={styles(screenWidth).cancelButton} onPress={onClose}>
                <Text style={styles(screenWidth).cancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles(screenWidth).saveButton} onPress={onSave}>
                <LinearGradient
                  colors={['#6366F1', '#8B5CF6']}
                  style={styles(screenWidth).saveGradient}
                >
                  <Text style={styles(screenWidth).saveText}>
                    {editingTask ? 'Update' : 'Create'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = (screenWidth: number) => {
  const localScale = (size: number) => (screenWidth / 375) * size; // Moved here
  return StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: localScale(20),
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: localScale(20),
    width: '100%',
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: localScale(10) },
    shadowOpacity: 0.25,
    shadowRadius: localScale(20),
    elevation: localScale(10),
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: localScale(20),
    borderBottomWidth: localScale(1),
    borderBottomColor: '#F3F4F6',
  },
  modalTitle: {
    fontSize: localScale(20),
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    padding: localScale(20),
    maxHeight: localScale(400),
  },
  inputGroup: {
    marginBottom: localScale(20),
  },
  inputLabel: {
    fontSize: localScale(16),
    fontWeight: '500',
    color: '#374151',
    marginBottom: localScale(8),
  },
  textInput: {
    borderWidth: localScale(1),
    borderColor: '#D1D5DB',
    borderRadius: localScale(12),
    paddingHorizontal: localScale(16),
    paddingVertical: localScale(12),
    fontSize: localScale(16),
    color: '#1F2937',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: localScale(80),
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: localScale(1),
    borderColor: '#D1D5DB',
    borderRadius: localScale(12),
    paddingHorizontal: localScale(16),
    paddingVertical: localScale(12),
    backgroundColor: '#FFFFFF',
    gap: localScale(12),
  },
  datePickerText: {
    fontSize: localScale(16),
    color: '#1F2937',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: localScale(8),
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: localScale(12),
    paddingHorizontal: localScale(16),
    borderWidth: localScale(2),
    borderRadius: localScale(12),
    backgroundColor: '#FFFFFF',
    gap: localScale(6),
  },
  priorityButtonActive: {
    backgroundColor: '#F9FAFB',
  },
  priorityButtonText: {
    fontSize: localScale(14),
    fontWeight: '500',
    color: '#6B7280',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: localScale(12),
  },
  checkbox: {
    width: localScale(20),
    height: localScale(20),
    borderWidth: localScale(2),
    borderColor: '#D1D5DB',
    borderRadius: localScale(4),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  checkboxActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  checkboxLabel: {
    fontSize: localScale(16),
    color: '#374151',
    flex: 1,
  },
  reminderTimeContainer: {
    marginTop: localScale(16),
    gap: localScale(12),
  },
  reminderTimeLabel: {
    fontSize: localScale(14),
    fontWeight: '500',
    color: '#6B7280',
  },
  dateTimePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: localScale(12),
  },
  dateTimePickerText: {
    fontSize: localScale(16),
    color: '#1F2937',
  },
  reminderIntervalContainer: {
    marginTop: localScale(16),
    gap: localScale(8),
  },
  reminderIntervalLabel: {
    fontSize: localScale(14),
    fontWeight: '500',
    color: '#6B7280',
  },
  intervalSelector: {
    flexDirection: 'row',
    gap: localScale(8),
  },
  intervalButton: {
    flex: 1,
    paddingVertical: localScale(8),
    paddingHorizontal: localScale(12),
    borderWidth: localScale(1),
    borderColor: '#D1D5DB',
    borderRadius: localScale(8),
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  intervalButtonActive: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  intervalButtonText: {
    fontSize: localScale(14),
    color: '#6B7280',
    fontWeight: '500',
  },
  intervalButtonActiveText: {
    color: '#FFFFFF',
  },
  reminderHint: {
    fontSize: localScale(12),
    color: '#9CA3AF',
    fontStyle: 'italic',
    marginTop: localScale(8),
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: localScale(20),
    borderTopWidth: localScale(1),
    borderTopColor: '#F3F4F6',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: localScale(8),
    paddingVertical: localScale(12),
    paddingHorizontal: localScale(16),
  },
  deleteText: {
    fontSize: localScale(16),
    color: '#EF4444',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: localScale(12),
  },
  cancelButton: {
    paddingVertical: localScale(12),
    paddingHorizontal: localScale(24),
    borderRadius: localScale(12),
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
  },
  cancelText: {
    fontSize: localScale(16),
    color: '#6B7280',
    fontWeight: '500',
  },
  saveButton: {
    borderRadius: localScale(12),
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: localScale(12),
    paddingHorizontal: localScale(24),
    alignItems: 'center',
  },
  saveText: {
    fontSize: localScale(16),
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
}

import { Theme, ColorScheme } from '@/types/theme';

// Light theme colors (existing)
const lightColors: ColorScheme = {
  background: {
    primary: '#F8FAFC',
    secondary: '#FFFFFF',
    tertiary: '#F1F5F9',
    card: '#FFFFFF',
    modal: '#FFFFFF',
  },
  text: {
    primary: '#1F2937',
    secondary: '#6B7280',
    disabled: '#9CA3AF',
    inverse: '#FFFFFF',
  },
  accent: {
    primary: '#BB86FC', // Deep purple as specified
    secondary: '#6366F1',
    hover: '#A855F7',
  },
  status: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  interactive: {
    button: {
      background: '#BB86FC',
      text: '#FFFFFF',
      hover: '#A855F7',
      disabled: '#E5E7EB',
    },
    input: {
      background: '#F9FAFB',
      border: '#E5E7EB',
      focusBorder: '#BB86FC',
      placeholder: '#9CA3AF',
    },
    link: {
      default: '#BB86FC',
      hover: '#A855F7',
    },
  },
  ui: {
    border: '#E5E7EB',
    shadow: '#000000',
    overlay: 'rgba(0, 0, 0, 0.5)',
    tabBar: {
      background: '#FFFFFF',
      border: '#E5E7EB',
      active: '#BB86FC',
      inactive: '#9CA3AF',
    },
  },
  gradients: {
    primary: ['#BB86FC', '#A855F7'],
    secondary: ['#6366F1', '#8B5CF6'],
    accent: ['#10B981', '#34D399'],
  },
};

// Dark theme colors (new)
const darkColors: ColorScheme = {
  background: {
    primary: '#121212', // Deep charcoal as specified
    secondary: '#1E1E1E', // Rich dark gray as specified
    tertiary: '#2A2A2A', // Slightly lighter for input fields
    card: '#1E1E1E',
    modal: '#1E1E1E',
  },
  text: {
    primary: '#FFFFFF', // Pure white as specified
    secondary: '#B3B3B3', // Light gray as specified
    disabled: '#808080', // Dark gray as specified
    inverse: '#121212',
  },
  accent: {
    primary: '#BB86FC', // Deep purple as specified
    secondary: '#D1C4E9', // 10% brightness increase for hover
    hover: '#D1C4E9',
  },
  status: {
    success: '#4ADE80',
    warning: '#FBBF24',
    error: '#F87171',
    info: '#60A5FA',
  },
  interactive: {
    button: {
      background: '#BB86FC',
      text: '#FFFFFF',
      hover: '#D1C4E9', // 10% brightness increase
      disabled: '#404040',
    },
    input: {
      background: '#2A2A2A', // Slightly lighter background as specified
      border: '#404040',
      focusBorder: '#BB86FC', // Accent-colored borders
      placeholder: '#808080',
    },
    link: {
      default: '#BB86FC',
      hover: '#D1C4E9',
    },
  },
  ui: {
    border: '#404040',
    shadow: '#000000',
    overlay: 'rgba(0, 0, 0, 0.6)', // Semi-transparent overlay as specified
    tabBar: {
      background: '#1E1E1E',
      border: '#404040',
      active: '#BB86FC',
      inactive: '#808080',
    },
  },
  gradients: {
    primary: ['#BB86FC', '#D1C4E9'],
    secondary: ['#8B5CF6', '#A855F7'],
    accent: ['#4ADE80', '#34D399'],
  },
};

// Common spacing, border radius, shadows, and transitions
const commonThemeProperties = {
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
    xl: 24,
    full: 9999,
  },
  transitions: {
    fast: 150,
    normal: 200, // 0.2s as specified
    slow: 300,
  },
};

// Light theme
export const lightTheme: Theme = {
  mode: 'light',
  colors: lightColors,
  ...commonThemeProperties,
  shadows: {
    sm: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.15,
      shadowRadius: 16,
      elevation: 8,
    },
  },
};

// Dark theme
export const darkTheme: Theme = {
  mode: 'dark',
  colors: darkColors,
  ...commonThemeProperties,
  shadows: {
    sm: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3, // Higher opacity for dark shadows
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4, // Subtle elevation with dark shadows
      shadowRadius: 4,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 8 },
      shadowOpacity: 0.5,
      shadowRadius: 16,
      elevation: 8,
    },
  },
};

export const themes = {
  light: lightTheme,
  dark: darkTheme,
};
